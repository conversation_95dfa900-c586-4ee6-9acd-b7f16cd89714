"""
VSCode清理工具
实现VSCode软件的保守清理功能，恢复到刚安装的纯净状态，但保留所有扩展
"""

import os
import shutil
import json
import time
import subprocess
import psutil
from pathlib import Path
from typing import Tuple, List, Optional, Dict
from datetime import datetime


class VSCodeCleaner:
    """VSCode清理工具类"""
    
    def __init__(self):
        self.user_profile = Path.home()
        
        # VSCode主要目录
        self.vscode_appdata = self.user_profile / "AppData" / "Roaming" / "Code"
        self.vscode_userdata = self.vscode_appdata / "User"
        self.extensions_dir = self.user_profile / ".vscode" / "extensions"
        
        # 备份目录
        self.backup_base_dir = Path("backups") / "vscode_backups"
        
        # 需要保护的扩展目录（绝对不能删除）
        self.protected_directories = {
            str(self.extensions_dir),  # 所有扩展
        }
        
        # 可以安全清理的目录和文件
        self.cleanable_directories = {
            "logs": self.vscode_appdata / "logs",
            "CachedExtensions": self.vscode_appdata / "CachedExtensions", 
            "CachedExtensionVSIXs": self.vscode_appdata / "CachedExtensionVSIXs",
            "backups": self.vscode_appdata / "backups",
            "Crashpad": self.vscode_appdata / "Crashpad",
            "History": self.vscode_userdata / "History",
            "workspaceStorage": self.vscode_userdata / "workspaceStorage",
            "sync": self.vscode_userdata / "sync",
        }
        
        # 可以清理的配置文件（会备份）
        self.cleanable_config_files = {
            "settings.json": self.vscode_userdata / "settings.json",
            "keybindings.json": self.vscode_userdata / "keybindings.json", 
            "tasks.json": self.vscode_userdata / "tasks.json",
            "launch.json": self.vscode_userdata / "launch.json",
            "locale.json": self.vscode_userdata / "locale.json",
            "argv.json": self.user_profile / ".vscode" / "argv.json",
            "machine_settings.json": self.user_profile / ".vscode" / "machine_settings.json",
        }
        
        # 需要谨慎处理的扩展数据目录
        self.extension_data_dirs = {
            "globalStorage": self.vscode_userdata / "globalStorage",
        }

    def find_vscode_processes(self) -> List[psutil.Process]:
        """查找所有VSCode相关进程"""
        vscode_processes = []
        
        # VSCode可能的进程名
        vscode_names = ["Code.exe", "code", "code.exe", "Code"]
        
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                proc_name = proc.info['name']
                proc_exe = proc.info['exe']
                
                # 检查进程名
                if proc_name in vscode_names:
                    vscode_processes.append(proc)
                    continue
                    
                # 检查可执行文件路径
                if proc_exe and any(name.lower() in proc_exe.lower() for name in ["code", "vscode"]):
                    if "Microsoft VS Code" in proc_exe:
                        vscode_processes.append(proc)
                        
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
                
        return vscode_processes

    def close_vscode_processes(self) -> Tuple[bool, str]:
        """关闭所有VSCode进程"""
        try:
            processes = self.find_vscode_processes()
            
            if not processes:
                return True, "未发现运行中的VSCode进程"
            
            # 尝试优雅关闭
            for proc in processes:
                try:
                    proc.terminate()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # 等待进程关闭
            time.sleep(3)
            
            # 检查是否还有进程存在
            remaining_processes = self.find_vscode_processes()
            
            if remaining_processes:
                # 强制关闭
                for proc in remaining_processes:
                    try:
                        proc.kill()
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                
                time.sleep(2)
                
                # 最终检查
                final_processes = self.find_vscode_processes()
                if final_processes:
                    return False, f"无法关闭 {len(final_processes)} 个VSCode进程"
            
            return True, f"成功关闭 {len(processes)} 个VSCode进程"
            
        except Exception as e:
            return False, f"关闭VSCode进程时出错: {str(e)}"

    def create_backup(self) -> Tuple[bool, str, Optional[Path]]:
        """创建配置文件备份"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_dir = self.backup_base_dir / f"vscode_backup_{timestamp}"
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            backup_count = 0
            
            # 备份配置文件
            for name, file_path in self.cleanable_config_files.items():
                if file_path.exists():
                    backup_file = backup_dir / name
                    backup_file.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(file_path, backup_file)
                    backup_count += 1
            
            # 备份snippets目录
            snippets_dir = self.vscode_userdata / "snippets"
            if snippets_dir.exists():
                backup_snippets = backup_dir / "snippets"
                shutil.copytree(snippets_dir, backup_snippets)
                backup_count += len(list(snippets_dir.glob("*")))
            
            return True, f"备份完成，共备份 {backup_count} 个文件到: {backup_dir}", backup_dir
            
        except Exception as e:
            return False, f"备份失败: {str(e)}", None

    def clean_vscode_conservative(self) -> Tuple[bool, str, Dict]:
        """执行保守清理"""
        try:
            cleaned_files = []
            cleaned_dirs = []
            protected_items = []
            errors = []
            
            # 清理目录
            for name, dir_path in self.cleanable_directories.items():
                if dir_path.exists():
                    try:
                        file_count = len(list(dir_path.rglob("*")))
                        shutil.rmtree(dir_path)
                        cleaned_dirs.append(f"{name}: {dir_path} ({file_count} 个文件)")
                    except Exception as e:
                        errors.append(f"清理目录 {name} 失败: {str(e)}")
            
            # 清理配置文件
            for name, file_path in self.cleanable_config_files.items():
                if file_path.exists():
                    try:
                        file_path.unlink()
                        cleaned_files.append(f"{name}: {file_path}")
                    except Exception as e:
                        errors.append(f"清理文件 {name} 失败: {str(e)}")
            
            # 清理snippets目录
            snippets_dir = self.vscode_userdata / "snippets"
            if snippets_dir.exists():
                try:
                    file_count = len(list(snippets_dir.glob("*")))
                    shutil.rmtree(snippets_dir)
                    cleaned_dirs.append(f"snippets: {snippets_dir} ({file_count} 个文件)")
                except Exception as e:
                    errors.append(f"清理snippets目录失败: {str(e)}")
            
            # 谨慎清理扩展数据（只清理明显的缓存）
            global_storage = self.vscode_userdata / "globalStorage"
            if global_storage.exists():
                cache_cleaned = 0
                for ext_dir in global_storage.iterdir():
                    if ext_dir.is_dir():
                        # 只清理明显的缓存文件
                        cache_files = list(ext_dir.glob("*cache*")) + list(ext_dir.glob("*temp*")) + list(ext_dir.glob("*log*"))
                        for cache_file in cache_files:
                            try:
                                if cache_file.is_file():
                                    cache_file.unlink()
                                    cache_cleaned += 1
                                elif cache_file.is_dir():
                                    shutil.rmtree(cache_file)
                                    cache_cleaned += 1
                            except Exception:
                                pass
                
                if cache_cleaned > 0:
                    cleaned_files.append(f"扩展缓存文件: {cache_cleaned} 个")
            
            # 记录保护的项目
            for protected_dir in self.protected_directories:
                if Path(protected_dir).exists():
                    ext_count = len(list(Path(protected_dir).iterdir()))
                    protected_items.append(f"扩展目录: {protected_dir} ({ext_count} 个扩展)")
            
            result = {
                "cleaned_files": cleaned_files,
                "cleaned_dirs": cleaned_dirs, 
                "protected_items": protected_items,
                "errors": errors,
                "total_cleaned": len(cleaned_files) + len(cleaned_dirs)
            }
            
            if errors:
                return False, f"清理完成但有 {len(errors)} 个错误", result
            else:
                return True, f"清理完成，共清理 {result['total_cleaned']} 个项目", result
                
        except Exception as e:
            return False, f"清理过程中出错: {str(e)}", {}

    def start_vscode(self) -> Tuple[bool, str]:
        """启动VSCode"""
        try:
            # 尝试多种启动方式
            vscode_paths = [
                "code",  # 如果在PATH中
                str(self.user_profile / "AppData" / "Local" / "Programs" / "Microsoft VS Code" / "Code.exe"),
                "C:\\Program Files\\Microsoft VS Code\\Code.exe",
                "C:\\Program Files (x86)\\Microsoft VS Code\\Code.exe",
            ]
            
            for vscode_path in vscode_paths:
                try:
                    subprocess.Popen([vscode_path], shell=True)
                    time.sleep(2)  # 等待启动
                    
                    # 检查是否成功启动
                    if self.find_vscode_processes():
                        return True, f"VSCode已成功启动: {vscode_path}"
                        
                except Exception:
                    continue
            
            return False, "无法启动VSCode，请手动启动"
            
        except Exception as e:
            return False, f"启动VSCode时出错: {str(e)}"

    def clean_and_restart_vscode(self) -> Tuple[bool, str, Dict]:
        """完整的清理和重启流程"""
        try:
            results = {
                "close_result": "",
                "backup_result": "",
                "backup_path": None,
                "clean_result": "",
                "clean_details": {},
                "restart_result": "",
                "total_time": 0
            }
            
            start_time = time.time()
            
            # 1. 关闭VSCode
            close_success, close_msg = self.close_vscode_processes()
            results["close_result"] = close_msg
            
            if not close_success:
                return False, f"无法关闭VSCode: {close_msg}", results
            
            # 2. 创建备份
            backup_success, backup_msg, backup_path = self.create_backup()
            results["backup_result"] = backup_msg
            results["backup_path"] = backup_path
            
            if not backup_success:
                return False, f"备份失败: {backup_msg}", results
            
            # 3. 执行清理
            clean_success, clean_msg, clean_details = self.clean_vscode_conservative()
            results["clean_result"] = clean_msg
            results["clean_details"] = clean_details
            
            if not clean_success:
                return False, f"清理失败: {clean_msg}", results
            
            # 4. 重启VSCode
            restart_success, restart_msg = self.start_vscode()
            results["restart_result"] = restart_msg
            
            results["total_time"] = round(time.time() - start_time, 2)
            
            if not restart_success:
                return False, f"清理成功但重启失败: {restart_msg}", results
            
            return True, "VSCode清理和重启完成", results
            
        except Exception as e:
            return False, f"清理流程出错: {str(e)}", {}

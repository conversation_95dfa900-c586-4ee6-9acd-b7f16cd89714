
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.json', '.'),
        ('.env', '.'),
        ('resources', 'resources'),
    ],
    hiddenimports=[
        'tkinter',
        'selenium',
        'cryptography',
        'aiohttp',
        'aiosqlite',
        'webdriver_manager',
        'fake_useragent',
        'colorlog',
        'pydantic',
        'asyncio_throttle',
        'dotenv',
        'PIL',
        'psutil',
        'email.mime.text',
        'email.mime.multipart',
        'imaplib',
        'smtplib',
        'pathlib',
        'shutil',
        'subprocess',
        'threading',
        'time',
        'json',
        'datetime',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='InfiniteEmailSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='resources/icons/app.ico' if os.path.exists('resources/icons/app.ico') else None,
)

"""
Augment VSCode扩展清理工具
功能：恢复Augment扩展到刚安装的纯净环境，但保留chat上下文
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import logging
from .logger import LoggerMixin


class AugmentCleaner(LoggerMixin):
    """Augment VSCode扩展清理器 - 安全清理临时数据，保留重要配置"""

    def __init__(self):
        self.vscode_data_dir = Path.home() / "AppData" / "Roaming" / "Code" / "User"
        self.global_storage_dir = self.vscode_data_dir / "globalStorage" / "augment.vscode-augment"
        self.workspace_storage_dir = self.vscode_data_dir / "workspaceStorage"
        self.backup_base_dir = Path.home() / "Desktop" / "Augment-Backups"

        # 定义重要数据，绝对不能删除
        self.critical_files = {
            'mcpServers.json',           # MCP服务配置
            'recentlyOpenedFiles.json',  # 最近打开文件
            'Augment-Memories'           # Chat对话历史
        }

        self.critical_directories = {
            'augment-global-state',      # 全局状态
            'augment-kv-store',          # 键值存储数据库
            'augment-user-assets',       # 用户资源文件
            'task-storage',              # 任务存储
            'tool-configs'               # 工具配置和规则
        }

        # 可以清理的临时数据（如果存在）
        self.cleanable_patterns = {
            'temp',
            'cache',
            'log',
            'tmp',
            '.log',
            '.tmp',
            '.cache'
        }

        # 确保备份目录存在
        self.backup_base_dir.mkdir(exist_ok=True)
    
    def get_current_backup_dir(self) -> Path:
        """获取当前备份目录"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = self.backup_base_dir / f"augment_backup_{timestamp}"
        backup_dir.mkdir(exist_ok=True)
        return backup_dir
    
    def find_augment_workspaces(self) -> List[Path]:
        """查找所有包含Augment数据的工作区"""
        augment_workspaces = []
        
        if not self.workspace_storage_dir.exists():
            self.logger.warning(f"工作区存储目录不存在: {self.workspace_storage_dir}")
            return augment_workspaces
        
        for workspace_dir in self.workspace_storage_dir.iterdir():
            if workspace_dir.is_dir():
                augment_dir = workspace_dir / "Augment.vscode-augment"
                if augment_dir.exists():
                    augment_workspaces.append(augment_dir)
                    self.logger.debug(f"发现Augment工作区: {augment_dir}")
        
        return augment_workspaces
    
    def backup_memories(self, backup_dir: Path) -> Dict[str, str]:
        """备份所有Memories文件"""
        memories_backup = {}
        
        # 备份全局存储中的Memories（如果存在）
        global_memories = self.global_storage_dir / "Augment-Memories"
        if global_memories.exists():
            backup_path = backup_dir / "global_memories.bak"
            shutil.copy2(global_memories, backup_path)
            memories_backup["global"] = str(backup_path)
            self.logger.info(f"已备份全局Memories: {backup_path}")
        
        # 备份工作区中的Memories
        workspaces = self.find_augment_workspaces()
        for i, workspace_dir in enumerate(workspaces):
            memories_file = workspace_dir / "Augment-Memories"
            if memories_file.exists():
                workspace_id = workspace_dir.parent.name
                backup_path = backup_dir / f"workspace_{i}_{workspace_id}_memories.bak"
                shutil.copy2(memories_file, backup_path)
                memories_backup[f"workspace_{i}"] = {
                    "backup_path": str(backup_path),
                    "original_path": str(memories_file),
                    "workspace_id": workspace_id
                }
                self.logger.info(f"已备份工作区Memories: {backup_path}")
        
        # 保存备份信息
        backup_info_file = backup_dir / "backup_info.json"
        with open(backup_info_file, 'w', encoding='utf-8') as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "memories_backup": memories_backup,
                "backup_type": "augment_cleaner"
            }, f, indent=2, ensure_ascii=False)
        
        return memories_backup
    
    def is_safe_to_clean(self, file_path: Path) -> bool:
        """判断文件是否可以安全清理"""
        file_name = file_path.name.lower()

        # 检查是否是重要文件
        if file_name in self.critical_files:
            return False

        # 检查是否是重要目录
        if file_path.is_dir() and file_name in self.critical_directories:
            return False

        # 检查是否是可清理的临时文件
        for pattern in self.cleanable_patterns:
            if pattern in file_name:
                return True

        # 默认不清理（安全策略）
        return False

    def clean_global_storage_safely(self) -> bool:
        """安全清理全局存储中的临时数据"""
        cleaned_files, _ = self.clean_global_storage_safely_detailed()
        return cleaned_files is not None

    def clean_global_storage_safely_detailed(self) -> Tuple[Optional[List[str]], List[str]]:
        """安全清理全局存储中的临时数据，返回详细信息"""
        try:
            if not self.global_storage_dir.exists():
                self.logger.info("全局存储目录不存在，跳过清理")
                return [], []

            cleaned_files = []
            protected_files = []

            # 遍历全局存储目录
            for item in self.global_storage_dir.rglob('*'):
                if item.is_file():
                    if self.is_safe_to_clean(item):
                        try:
                            item.unlink()
                            cleaned_files.append(str(item))
                            self.logger.info(f"已清理临时文件: {item}")
                        except Exception as e:
                            self.logger.warning(f"清理文件失败 {item}: {e}")
                    else:
                        protected_files.append(str(item))
                        self.logger.info(f"保护重要文件: {item}")

            self.logger.info(f"全局存储清理完成，清理 {len(cleaned_files)} 个临时文件，保护 {len(protected_files)} 个重要文件")
            return cleaned_files, protected_files

        except Exception as e:
            self.logger.error(f"清理全局存储失败: {e}")
            return None, []
    
    def clean_workspace_storage_safely(self) -> bool:
        """安全清理工作区存储中的临时数据"""
        cleaned_files, _ = self.clean_workspace_storage_safely_detailed()
        return cleaned_files is not None

    def clean_workspace_storage_safely_detailed(self) -> Tuple[Optional[List[str]], List[str]]:
        """安全清理工作区存储中的临时数据，返回详细信息"""
        try:
            workspaces = self.find_augment_workspaces()
            cleaned_files = []
            protected_files = []

            for workspace_dir in workspaces:
                workspace_id = workspace_dir.parent.name

                # 遍历工作区目录，只清理临时文件
                for item in workspace_dir.rglob('*'):
                    if item.is_file():
                        if self.is_safe_to_clean(item):
                            try:
                                item.unlink()
                                cleaned_files.append(str(item))
                                self.logger.info(f"已清理工作区临时文件: {item}")
                            except Exception as e:
                                self.logger.warning(f"清理文件失败 {item}: {e}")
                        else:
                            protected_files.append(str(item))
                            self.logger.info(f"保护工作区重要文件: {item}")

                self.logger.info(f"工作区 {workspace_id} 处理完成")

            self.logger.info(f"所有工作区处理完成，清理 {len(cleaned_files)} 个临时文件，保护 {len(protected_files)} 个重要文件")
            return cleaned_files, protected_files

        except Exception as e:
            self.logger.error(f"清理工作区存储失败: {e}")
            return None, []
    
    def restore_from_backup(self, backup_dir: Path) -> bool:
        """从备份恢复Memories文件"""
        try:
            backup_info_file = backup_dir / "backup_info.json"
            if not backup_info_file.exists():
                self.logger.error(f"备份信息文件不存在: {backup_info_file}")
                return False
            
            with open(backup_info_file, 'r', encoding='utf-8') as f:
                backup_info = json.load(f)
            
            memories_backup = backup_info.get("memories_backup", {})
            
            # 恢复全局Memories
            if "global" in memories_backup:
                global_backup = Path(memories_backup["global"])
                if global_backup.exists():
                    self.global_storage_dir.mkdir(parents=True, exist_ok=True)
                    target_file = self.global_storage_dir / "Augment-Memories"
                    shutil.copy2(global_backup, target_file)
                    self.logger.info(f"已恢复全局Memories: {target_file}")
            
            # 恢复工作区Memories
            for key, backup_data in memories_backup.items():
                if key.startswith("workspace_") and isinstance(backup_data, dict):
                    backup_path = Path(backup_data["backup_path"])
                    original_path = Path(backup_data["original_path"])
                    
                    if backup_path.exists():
                        original_path.parent.mkdir(parents=True, exist_ok=True)
                        shutil.copy2(backup_path, original_path)
                        self.logger.info(f"已恢复工作区Memories: {original_path}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"从备份恢复失败: {e}")
            return False
    
    def clean_augment_temp_data(self, create_backup: bool = True) -> Tuple[bool, str, Optional[Path]]:
        """
        安全清理Augment临时数据，保留所有重要配置

        Args:
            create_backup: 是否创建备份

        Returns:
            (成功状态, 消息, 备份目录路径)
        """
        success, message, backup_dir, _ = self.clean_augment_temp_data_detailed(create_backup)
        return success, message, backup_dir

    def clean_augment_temp_data_detailed(self, create_backup: bool = True) -> Tuple[bool, str, Optional[Path], Dict]:
        """
        安全清理Augment临时数据，返回详细信息

        Args:
            create_backup: 是否创建备份

        Returns:
            (成功状态, 消息, 备份目录路径, 详细信息字典)
        """
        try:
            self.logger.info("开始安全清理Augment扩展临时数据...")

            backup_dir = None
            cleaned_files = []
            protected_files = []
            backup_count = 0

            # 1. 创建备份目录（可选）
            if create_backup:
                backup_dir = self.get_current_backup_dir()
                self.logger.info(f"创建备份目录: {backup_dir}")

                # 备份重要文件（预防措施）
                memories_backup = self.backup_memories(backup_dir)
                backup_count = len(memories_backup)
                self.logger.info(f"已备份 {backup_count} 个重要文件")

            # 2. 安全清理全局存储临时数据
            global_cleaned, global_protected = self.clean_global_storage_safely_detailed()
            if global_cleaned is None:
                return False, "清理全局存储临时数据失败", backup_dir, {}

            cleaned_files.extend(global_cleaned)
            protected_files.extend(global_protected)

            # 3. 安全清理工作区存储临时数据
            workspace_cleaned, workspace_protected = self.clean_workspace_storage_safely_detailed()
            if workspace_cleaned is None:
                return False, "清理工作区存储临时数据失败", backup_dir, {}

            cleaned_files.extend(workspace_cleaned)
            protected_files.extend(workspace_protected)

            # 准备详细信息
            details = {
                'backup_count': backup_count,
                'cleaned_count': len(cleaned_files),
                'protected_count': len(protected_files),
                'cleaned_files': cleaned_files,
                'protected_files': protected_files
            }

            success_msg = f"Augment临时数据清理完成！"

            self.logger.info("Augment临时数据清理操作完成")
            return True, success_msg, backup_dir, details

        except Exception as e:
            error_msg = f"清理过程中发生错误: {e}"
            self.logger.error(error_msg)
            return False, error_msg, backup_dir, {}
    
    def get_augment_status(self) -> Dict[str, any]:
        """获取Augment扩展当前状态"""
        status = {
            "global_storage_exists": self.global_storage_dir.exists(),
            "global_storage_size": 0,
            "workspace_count": 0,
            "total_memories_files": 0,
            "last_activity": None
        }
        
        try:
            # 检查全局存储
            if status["global_storage_exists"]:
                status["global_storage_size"] = sum(
                    f.stat().st_size for f in self.global_storage_dir.rglob('*') if f.is_file()
                )
            
            # 检查工作区
            workspaces = self.find_augment_workspaces()
            status["workspace_count"] = len(workspaces)
            
            # 统计Memories文件
            for workspace_dir in workspaces:
                memories_file = workspace_dir / "Augment-Memories"
                if memories_file.exists():
                    status["total_memories_files"] += 1
                    
                    # 获取最后活动时间
                    mtime = memories_file.stat().st_mtime
                    last_modified = datetime.fromtimestamp(mtime)
                    if not status["last_activity"] or last_modified > status["last_activity"]:
                        status["last_activity"] = last_modified
            
            # 检查全局Memories
            global_memories = self.global_storage_dir / "Augment-Memories"
            if global_memories.exists():
                status["total_memories_files"] += 1
        
        except Exception as e:
            self.logger.error(f"获取Augment状态失败: {e}")
        
        return status

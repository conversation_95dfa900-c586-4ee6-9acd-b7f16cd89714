#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VSCode目录结构分析器
分析VSCode软件目录，制定清理方案
"""

import os
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import stat

class VSCodeAnalyzer:
    """VSCode目录结构分析器"""
    
    def __init__(self):
        self.user_profile = Path.home()
        
        # VSCode主要目录路径
        self.vscode_appdata = self.user_profile / "AppData" / "Roaming" / "Code"
        self.vscode_localdata = self.user_profile / "AppData" / "Local" / "Programs" / "Microsoft VS Code"
        self.vscode_user_dir = self.vscode_appdata / "User"
        
        # 扩展相关目录
        self.extensions_dir = self.user_profile / ".vscode" / "extensions"
        
        # 分析结果
        self.analysis_result = {}
        
    def analyze_directory_structure(self) -> Dict:
        """分析VSCode完整目录结构"""
        print("🔍 开始分析VSCode目录结构...")
        
        analysis = {
            "timestamp": datetime.now().isoformat(),
            "directories": {},
            "summary": {},
            "cleanup_recommendations": {}
        }
        
        # 分析主要目录
        directories_to_analyze = [
            ("AppData/Roaming/Code", self.vscode_appdata),
            ("AppData/Local/Programs/Microsoft VS Code", self.vscode_localdata),
            ("User/.vscode/extensions", self.extensions_dir)
        ]
        
        for name, path in directories_to_analyze:
            print(f"📁 分析目录: {name}")
            if path.exists():
                analysis["directories"][name] = self._analyze_directory(path)
            else:
                analysis["directories"][name] = {"exists": False, "reason": "目录不存在"}
                print(f"   ❌ 目录不存在: {path}")
        
        # 生成总结和建议
        analysis["summary"] = self._generate_summary(analysis["directories"])
        analysis["cleanup_recommendations"] = self._generate_cleanup_recommendations(analysis["directories"])
        
        self.analysis_result = analysis
        return analysis
    
    def _analyze_directory(self, directory: Path) -> Dict:
        """分析单个目录"""
        try:
            result = {
                "exists": True,
                "path": str(directory),
                "total_size": 0,
                "file_count": 0,
                "subdirectories": {},
                "important_files": [],
                "last_modified": None
            }
            
            if not directory.exists():
                result["exists"] = False
                return result
            
            # 获取目录统计信息
            total_size = 0
            file_count = 0
            latest_modified = None
            
            # 分析子目录和文件
            for item in directory.iterdir():
                try:
                    if item.is_file():
                        file_size = item.stat().st_size
                        total_size += file_size
                        file_count += 1
                        
                        modified_time = datetime.fromtimestamp(item.stat().st_mtime)
                        if latest_modified is None or modified_time > latest_modified:
                            latest_modified = modified_time
                        
                        # 识别重要文件
                        if self._is_important_file(item):
                            result["important_files"].append({
                                "name": item.name,
                                "path": str(item),
                                "size": file_size,
                                "modified": modified_time.isoformat()
                            })
                    
                    elif item.is_dir():
                        # 递归分析子目录（限制深度）
                        subdir_info = self._analyze_subdirectory(item, max_depth=2)
                        result["subdirectories"][item.name] = subdir_info
                        total_size += subdir_info.get("total_size", 0)
                        file_count += subdir_info.get("file_count", 0)
                        
                        subdir_modified = subdir_info.get("last_modified")
                        if subdir_modified:
                            subdir_modified_dt = datetime.fromisoformat(subdir_modified)
                            if latest_modified is None or subdir_modified_dt > latest_modified:
                                latest_modified = subdir_modified_dt
                
                except (PermissionError, OSError) as e:
                    print(f"   ⚠️ 无法访问: {item} - {e}")
                    continue
            
            result["total_size"] = total_size
            result["file_count"] = file_count
            if latest_modified:
                result["last_modified"] = latest_modified.isoformat()
            
            return result
            
        except Exception as e:
            return {
                "exists": False,
                "error": str(e),
                "path": str(directory)
            }
    
    def _analyze_subdirectory(self, directory: Path, max_depth: int = 2, current_depth: int = 0) -> Dict:
        """分析子目录（限制递归深度）"""
        if current_depth >= max_depth:
            return {"name": directory.name, "depth_limited": True}
        
        try:
            result = {
                "name": directory.name,
                "path": str(directory),
                "total_size": 0,
                "file_count": 0,
                "subdirectories": {},
                "category": self._categorize_directory(directory),
                "last_modified": None
            }
            
            total_size = 0
            file_count = 0
            latest_modified = None
            
            for item in directory.iterdir():
                try:
                    if item.is_file():
                        file_size = item.stat().st_size
                        total_size += file_size
                        file_count += 1
                        
                        modified_time = datetime.fromtimestamp(item.stat().st_mtime)
                        if latest_modified is None or modified_time > latest_modified:
                            latest_modified = modified_time
                    
                    elif item.is_dir() and current_depth < max_depth - 1:
                        subdir_info = self._analyze_subdirectory(item, max_depth, current_depth + 1)
                        result["subdirectories"][item.name] = subdir_info
                        total_size += subdir_info.get("total_size", 0)
                        file_count += subdir_info.get("file_count", 0)
                
                except (PermissionError, OSError):
                    continue
            
            result["total_size"] = total_size
            result["file_count"] = file_count
            if latest_modified:
                result["last_modified"] = latest_modified.isoformat()
            
            return result
            
        except Exception as e:
            return {
                "name": directory.name,
                "error": str(e),
                "path": str(directory)
            }
    
    def _is_important_file(self, file_path: Path) -> bool:
        """判断是否为重要文件"""
        important_files = {
            'settings.json',
            'keybindings.json',
            'tasks.json',
            'launch.json',
            'extensions.json',
            'locale.json'
        }
        
        return file_path.name in important_files
    
    def _categorize_directory(self, directory: Path) -> str:
        """对目录进行分类"""
        dir_name = directory.name.lower()
        
        # 用户配置相关
        if dir_name in ['user', 'settings']:
            return "用户配置"
        
        # 扩展相关
        elif dir_name in ['extensions', 'globalStorage', 'workspaceStorage']:
            return "扩展数据"
        
        # 缓存和临时文件
        elif any(keyword in dir_name for keyword in ['cache', 'temp', 'tmp', 'log']):
            return "缓存临时"
        
        # 工作区相关
        elif 'workspace' in dir_name:
            return "工作区数据"
        
        # 备份相关
        elif 'backup' in dir_name:
            return "备份数据"
        
        else:
            return "其他"
    
    def _generate_summary(self, directories: Dict) -> Dict:
        """生成分析总结"""
        summary = {
            "total_directories": len([d for d in directories.values() if d.get("exists", False)]),
            "total_size": 0,
            "total_files": 0,
            "categories": {}
        }
        
        for dir_info in directories.values():
            if dir_info.get("exists", False):
                summary["total_size"] += dir_info.get("total_size", 0)
                summary["total_files"] += dir_info.get("file_count", 0)
        
        return summary
    
    def _generate_cleanup_recommendations(self, directories: Dict) -> Dict:
        """生成清理建议"""
        recommendations = {
            "safe_to_clean": [],
            "preserve_always": [],
            "clean_with_caution": [],
            "cleanup_strategy": {}
        }
        
        # 基于目录分析生成建议
        for dir_name, dir_info in directories.items():
            if not dir_info.get("exists", False):
                continue
            
            # 分析子目录
            for subdir_name, subdir_info in dir_info.get("subdirectories", {}).items():
                category = subdir_info.get("category", "其他")
                
                if category == "缓存临时":
                    recommendations["safe_to_clean"].append({
                        "path": subdir_info.get("path", ""),
                        "reason": "临时缓存文件，可安全清理",
                        "size": subdir_info.get("total_size", 0)
                    })
                
                elif category == "用户配置":
                    recommendations["preserve_always"].append({
                        "path": subdir_info.get("path", ""),
                        "reason": "用户配置文件，必须保留",
                        "size": subdir_info.get("total_size", 0)
                    })
                
                elif category == "扩展数据":
                    recommendations["clean_with_caution"].append({
                        "path": subdir_info.get("path", ""),
                        "reason": "扩展数据，需要谨慎处理",
                        "size": subdir_info.get("total_size", 0)
                    })
        
        return recommendations
    
    def print_analysis_report(self):
        """打印分析报告"""
        if not self.analysis_result:
            print("❌ 请先运行 analyze_directory_structure()")
            return
        
        print("\n" + "="*80)
        print("📊 VSCode目录结构分析报告")
        print("="*80)
        
        # 基本信息
        summary = self.analysis_result.get("summary", {})
        print(f"\n📈 总体统计：")
        print(f"   • 分析目录数量：{summary.get('total_directories', 0)}")
        print(f"   • 总文件数量：{summary.get('total_files', 0):,}")
        print(f"   • 总占用空间：{self._format_size(summary.get('total_size', 0))}")
        
        # 目录详情
        print(f"\n📁 目录详情：")
        for dir_name, dir_info in self.analysis_result.get("directories", {}).items():
            if dir_info.get("exists", False):
                size = dir_info.get("total_size", 0)
                files = dir_info.get("file_count", 0)
                print(f"   ✅ {dir_name}")
                print(f"      路径：{dir_info.get('path', 'N/A')}")
                print(f"      大小：{self._format_size(size)}")
                print(f"      文件：{files:,} 个")
                
                # 显示重要子目录
                subdirs = dir_info.get("subdirectories", {})
                if subdirs:
                    print(f"      子目录：{len(subdirs)} 个")
                    for subdir_name, subdir_info in list(subdirs.items())[:5]:  # 只显示前5个
                        subdir_size = subdir_info.get("total_size", 0)
                        subdir_category = subdir_info.get("category", "其他")
                        print(f"        • {subdir_name} ({subdir_category}) - {self._format_size(subdir_size)}")
                    if len(subdirs) > 5:
                        print(f"        ... 还有 {len(subdirs) - 5} 个子目录")
            else:
                print(f"   ❌ {dir_name} - 不存在")
        
        # 清理建议
        recommendations = self.analysis_result.get("cleanup_recommendations", {})
        print(f"\n🧹 清理建议：")
        
        safe_clean = recommendations.get("safe_to_clean", [])
        if safe_clean:
            print(f"   ✅ 可安全清理 ({len(safe_clean)} 项)：")
            total_safe_size = sum(item.get("size", 0) for item in safe_clean)
            print(f"      总大小：{self._format_size(total_safe_size)}")
            for item in safe_clean[:3]:  # 只显示前3个
                print(f"      • {Path(item.get('path', '')).name} - {item.get('reason', '')}")
            if len(safe_clean) > 3:
                print(f"      ... 还有 {len(safe_clean) - 3} 项")
        
        preserve = recommendations.get("preserve_always", [])
        if preserve:
            print(f"   🛡️ 必须保留 ({len(preserve)} 项)：")
            for item in preserve[:3]:  # 只显示前3个
                print(f"      • {Path(item.get('path', '')).name} - {item.get('reason', '')}")
            if len(preserve) > 3:
                print(f"      ... 还有 {len(preserve) - 3} 项")
        
        caution = recommendations.get("clean_with_caution", [])
        if caution:
            print(f"   ⚠️ 谨慎处理 ({len(caution)} 项)：")
            for item in caution[:3]:  # 只显示前3个
                print(f"      • {Path(item.get('path', '')).name} - {item.get('reason', '')}")
            if len(caution) > 3:
                print(f"      ... 还有 {len(caution) - 3} 项")
    
    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        size = float(size_bytes)
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        return f"{size:.1f} {units[unit_index]}"


def main():
    """主函数"""
    print("🚀 VSCode目录结构分析器")
    print("=" * 50)
    
    analyzer = VSCodeAnalyzer()
    
    try:
        # 执行分析
        analysis_result = analyzer.analyze_directory_structure()
        
        # 打印报告
        analyzer.print_analysis_report()
        
        # 保存分析结果到文件
        output_file = Path("vscode_analysis_result.json")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 分析结果已保存到: {output_file}")
        print("\n✅ 分析完成！")
        
    except Exception as e:
        print(f"\n❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

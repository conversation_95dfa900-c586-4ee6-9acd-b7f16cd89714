# Augment安全清理功能使用指南

## 功能概述

Augment安全清理功能已成功集成到无限邮箱系统中，可以安全地清理Augment VSCode扩展的临时数据，同时保留所有重要的配置和数据。

⚠️ **重要说明**：此功能只清理临时数据，不会删除任何重要配置！

## 主要特性

### ✅ 安全清理策略
- 🛡️ **绝对保留**：MCP服务配置、工具规则、Chat历史、任务数据、用户资源
- 🧹 **安全清理**：临时缓存、日志文件、其他临时数据
- 💾 **自动备份**：清理前自动创建完整备份
- 📝 **详细日志**：完整的操作记录和状态跟踪

### 📁 备份机制
- 备份位置：`C:\Users\<USER>\Desktop\Augment-Backups\`
- 备份命名：`augment_backup_YYYYMMDD_HHMMSS`
- 包含备份信息文件：`backup_info.json`
- 支持从备份恢复

### 🎯 清理范围

**🛡️ 绝对保留的重要数据：**
- `mcpServers.json` - MCP服务配置
- `augment-kv-store/` - 键值存储数据库
- `augment-user-assets/` - 用户资源文件
- `task-storage/` - 任务存储数据
- `tool-configs/approval/` - 工具配置和规则
- `Augment-Memories` - Chat对话历史
- `recentlyOpenedFiles.json` - 最近打开文件

**🧹 会清理的临时数据：**
- 包含 `temp`、`cache`、`log`、`tmp` 等关键词的文件
- 临时缓存文件
- 日志文件（如果存在）
- 其他临时数据文件

## 使用方法

### 1. 通过GUI界面使用

1. 启动无限邮箱系统：
   ```bash
   cd infinite_email_system
   python main.py
   ```

2. 在主界面点击 **"清理Augment临时数据"** 按钮

3. 在确认对话框中选择：
   - **"是"**：执行安全清理（保留所有重要配置）
   - **"否"**：取消操作

4. 等待清理完成，查看输出区域的详细信息

5. 清理完成后，建议重启VSCode

### 2. 通过测试脚本验证

运行测试脚本查看当前状态：
```bash
cd infinite_email_system
python test_augment_cleaner.py
```

## 测试结果示例

根据最新测试，系统发现：
- **全局存储**：存在，大小32,480字节
- **工作区数量**：5个
- **Memories文件**：5个（其中1个包含90字节的对话数据）
- **最后活动时间**：2025-07-30 18:40:15

## 备份文件说明

### 备份目录结构
```
Augment-Backups/
└── augment_backup_20250731_002414/
    ├── backup_info.json                           # 备份信息文件
    ├── workspace_0_[hash]_memories.bak            # 工作区1的Memories备份
    ├── workspace_1_[hash]_memories.bak            # 工作区2的Memories备份
    ├── workspace_2_[hash]_memories.bak            # 工作区3的Memories备份（90字节）
    ├── workspace_3_[hash]_memories.bak            # 工作区4的Memories备份
    └── workspace_4_[hash]_memories.bak            # 工作区5的Memories备份
```

### backup_info.json 内容
```json
{
  "timestamp": "2025-07-31T00:24:14.123456",
  "memories_backup": {
    "workspace_0": {
      "backup_path": "...",
      "original_path": "...",
      "workspace_id": "..."
    }
  },
  "backup_type": "augment_cleaner"
}
```

## 安全注意事项

### ⚠️ 重要提醒
1. **清理前确认**：确保当前没有重要的未保存工作
2. **备份验证**：清理前会自动创建备份，请确认备份成功
3. **VSCode重启**：清理后必须重启VSCode才能生效
4. **网络连接**：清理过程中建议保持网络连接稳定

### 🔄 恢复方法
如果需要恢复数据：
1. 找到对应的备份目录
2. 查看 `backup_info.json` 了解备份详情
3. 手动复制备份文件到原位置
4. 重启VSCode

## 故障排除

### 常见问题

**Q: 清理后VSCode中看不到变化？**
A: 请完全关闭VSCode并重新启动。

**Q: 备份文件在哪里？**
A: 默认位置：`C:\Users\<USER>\Desktop\Augment-Backups\`

**Q: 如何确认清理是否成功？**
A: 查看输出区域的详细日志，或运行测试脚本验证。

**Q: 清理失败怎么办？**
A: 检查错误信息，确保有足够的磁盘空间和权限，必要时从备份恢复。

### 错误代码说明

- **权限错误**：以管理员身份运行程序
- **磁盘空间不足**：清理磁盘空间后重试
- **文件占用**：关闭VSCode后重试

## 技术实现

### 核心组件
- `utils/augment_cleaner.py`：核心清理逻辑
- `main.py`：GUI集成
- `test_augment_cleaner.py`：功能测试

### 关键方法
- `clean_augment_to_fresh_state()`：主清理方法
- `backup_memories()`：备份Memories文件
- `get_augment_status()`：获取当前状态
- `restore_from_backup()`：从备份恢复

## 更新日志

### v1.0.0 (2025-07-31)
- ✅ 初始版本发布
- ✅ 基础清理功能
- ✅ 备份和恢复机制
- ✅ GUI界面集成
- ✅ 完整测试覆盖

---

**开发者：** Augment Agent  
**集成日期：** 2025-07-31  
**版本：** 1.0.0

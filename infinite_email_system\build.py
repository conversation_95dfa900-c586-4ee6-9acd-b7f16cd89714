"""
打包构建脚本
"""

import PyInstaller.__main__
import os
import shutil
import sys
from pathlib import Path
import subprocess


def clean_build_directories():
    """清理构建目录"""
    print("清理之前的构建文件...")
    
    directories_to_clean = ['dist', 'build', '__pycache__']
    
    for directory in directories_to_clean:
        if os.path.exists(directory):
            shutil.rmtree(directory)
            print(f"已删除目录: {directory}")
    
    # 清理.pyc文件
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                os.remove(os.path.join(root, file))


def check_dependencies():
    """检查依赖"""
    print("检查依赖包...")

    # 包名映射：pip包名 -> 实际导入名
    package_mapping = {
        'requests': 'requests',
        'aiohttp': 'aiohttp',
        'selenium': 'selenium',
        'cryptography': 'cryptography',
        'Pillow': 'PIL',
        'PyInstaller': 'PyInstaller',
        'colorlog': 'colorlog',
        'aiosqlite': 'aiosqlite',
        'pydantic': 'pydantic',
        'webdriver-manager': 'webdriver_manager',
        'python-dotenv': 'dotenv',
        'asyncio-throttle': 'asyncio_throttle',
        'fake-useragent': 'fake_useragent',
        'psutil': 'psutil'
    }

    missing_packages = []

    for pip_name, import_name in package_mapping.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(pip_name)

    if missing_packages:
        print(f"缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False

    print("所有依赖包已安装")
    return True


def create_spec_file():
    """创建PyInstaller spec文件"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.json', '.'),
        ('.env', '.'),
        ('resources', 'resources'),
    ],
    hiddenimports=[
        'tkinter',
        'selenium',
        'cryptography',
        'aiohttp',
        'aiosqlite',
        'webdriver_manager',
        'fake_useragent',
        'colorlog',
        'pydantic',
        'asyncio_throttle',
        'dotenv',
        'PIL',
        'psutil',
        'email.mime.text',
        'email.mime.multipart',
        'imaplib',
        'smtplib',
        'pathlib',
        'shutil',
        'subprocess',
        'threading',
        'time',
        'json',
        'datetime',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='InfiniteEmailSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='resources/icons/app.ico' if os.path.exists('resources/icons/app.ico') else None,
)
'''
    
    with open('InfiniteEmailSystem.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("已创建spec文件")


def build_application():
    """构建应用程序"""
    print("开始构建应用程序...")
    
    # 使用spec文件构建
    if os.path.exists('InfiniteEmailSystem.spec'):
        args = [
            'InfiniteEmailSystem.spec',
            '--clean',
            '--noconfirm'
        ]
    else:
        # 直接构建参数
        args = [
            'main.py',
            '--onefile',
            '--windowed',
            '--name=InfiniteEmailSystem',
            '--distpath=dist',
            '--workpath=build',
            '--clean',
            '--noconfirm',
            '--add-data=config.json;.',
            '--add-data=.env;.',
            '--add-data=resources;resources',
            '--hidden-import=tkinter',
            '--hidden-import=selenium',
            '--hidden-import=cryptography',
            '--hidden-import=aiohttp',
            '--hidden-import=aiosqlite',
            '--hidden-import=webdriver_manager',
            '--hidden-import=fake_useragent',
            '--hidden-import=colorlog',
            '--hidden-import=pydantic',
            '--hidden-import=asyncio_throttle',
            '--hidden-import=dotenv',
            '--hidden-import=PIL',
            '--hidden-import=psutil',
            '--hidden-import=pathlib',
            '--hidden-import=shutil',
            '--hidden-import=subprocess',
            '--hidden-import=threading',
            '--hidden-import=time',
            '--hidden-import=json',
            '--hidden-import=datetime',
        ]
        
        # 添加图标（如果存在）
        icon_path = Path('resources/icons/app.ico')
        if icon_path.exists():
            args.append(f'--icon={icon_path}')
    
    # 执行PyInstaller
    try:
        PyInstaller.__main__.run(args)
        print("PyInstaller构建完成")
        return True
    except Exception as e:
        print(f"PyInstaller构建失败: {e}")
        return False


def post_build_setup():
    """构建后设置"""
    print("执行构建后设置...")
    
    dist_path = Path('dist')
    
    if not dist_path.exists():
        print("错误：dist目录不存在")
        return False
    
    # 查找可执行文件
    exe_files = list(dist_path.glob('*.exe'))
    if not exe_files:
        print("错误：未找到可执行文件")
        return False
    
    exe_path = exe_files[0]
    print(f"找到可执行文件: {exe_path}")
    
    # 复制必要文件到dist目录
    files_to_copy = [
        'config.json',
        '.env',
        'README.md'
    ]

    for file_name in files_to_copy:
        src_path = Path(file_name)
        if src_path.exists():
            dst_path = dist_path / file_name
            shutil.copy2(src_path, dst_path)
            print(f"已复制: {file_name}")
    
    # 创建必要目录
    directories_to_create = ['data', 'logs']
    for directory in directories_to_create:
        dir_path = dist_path / directory
        dir_path.mkdir(exist_ok=True)
        print(f"已创建目录: {directory}")
    
    # 复制resources目录（如果不是通过--add-data添加的话）
    resources_src = Path('resources')
    resources_dst = dist_path / 'resources'

    if resources_src.exists() and not resources_dst.exists():
        shutil.copytree(resources_src, resources_dst)
        print("已复制resources目录")
    
    # 创建启动脚本
    create_launcher_script(dist_path)
    
    # 创建安装说明
    create_installation_guide(dist_path)
    
    print("构建后设置完成")
    return True


def create_launcher_script(dist_path):
    """创建启动脚本"""
    launcher_content = '''@echo off
echo 正在启动无限邮箱系统...
echo.

REM 检查是否存在必要文件
if not exist "InfiniteEmailSystem.exe" (
    echo 错误：找不到主程序文件
    pause
    exit /b 1
)

REM 创建必要目录
if not exist "data" mkdir data
if not exist "logs" mkdir logs

REM 启动程序
start "" "InfiniteEmailSystem.exe"

echo 程序已启动
timeout /t 3 /nobreak >nul
'''
    
    launcher_path = dist_path / 'start.bat'
    with open(launcher_path, 'w', encoding='gbk') as f:
        f.write(launcher_content)
    
    print("已创建启动脚本: start.bat")


def create_installation_guide(dist_path):
    """创建安装说明"""
    guide_content = '''# 无限邮箱系统 - 安装和使用指南

## 系统要求
- Windows 10 或更高版本
- 网络连接

## 安装步骤
1. 解压所有文件到任意目录
2. 直接双击 `InfiniteEmailSystem.exe` 启动程序
3. 首次运行会自动创建必要的配置文件

## 启动方式
- **推荐**：直接双击 `InfiniteEmailSystem.exe`
- **备选**：双击 `start.bat`（提供额外的启动检查）

## 配置说明
1. 编辑 `config.json` 文件配置基本参数
2. 编辑 `.env` 文件配置敏感信息（如API密钥）
3. 重启程序使配置生效

## 主要功能
- 随机生成域名和邮箱地址
- 自动域名注册（Freenom）
- 邮件转发设置（Cloudflare）
- 批量管理和监控
- Augment扩展临时数据清理
- VSCode完整清理（保留扩展）

## 注意事项
- 首次使用请先配置Cloudflare API令牌
- 建议定期备份data目录中的数据库文件
- 遇到问题请查看logs目录中的日志文件
- 使用VSCode清理功能前请确保已保存重要工作
- Augment清理功能会保护MCP服务和聊天历史

## 技术支持
如有问题，请查看日志文件或联系技术支持。

---
无限邮箱系统 v1.0.0
'''
    
    guide_path = dist_path / '使用说明.txt'
    with open(guide_path, 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("已创建使用说明")


def create_installer():
    """创建安装包（可选）"""
    print("创建安装包功能暂未实现")
    # 这里可以集成NSIS或其他安装包制作工具


def main():
    """主函数"""
    print("=" * 50)
    print("无限邮箱系统 - 构建脚本")
    print("=" * 50)
    
    try:
        # 1. 清理构建目录
        clean_build_directories()
        
        # 2. 检查依赖
        if not check_dependencies():
            return False
        
        # 3. 创建spec文件
        create_spec_file()
        
        # 4. 构建应用程序
        if not build_application():
            return False
        
        # 5. 构建后设置
        if not post_build_setup():
            return False
        
        print("\n" + "=" * 50)
        print("构建完成！")
        print("可执行文件位于: dist/InfiniteEmailSystem.exe")
        print("使用 start.bat 启动程序")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"构建过程中出现错误: {e}")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
